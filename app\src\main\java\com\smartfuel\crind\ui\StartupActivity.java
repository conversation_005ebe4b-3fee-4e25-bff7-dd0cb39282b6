package com.smartfuel.crind.ui;

import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.StrictMode;

import androidx.appcompat.app.AlertDialog;

import com.smartfuel.crind.R;
import com.smartfuel.crind.app.AppContext;
import com.smartfuel.crind.consts.ApplicationConst;
import com.smartfuel.service.IServiceEvents;

public class StartupActivity extends BaseActivity implements IServiceEvents {

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            gradePriceUpdate();
            getService().LogonForecourtController();
        }catch (Exception e){
            disconnectToService();
            e.printStackTrace();
        }
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
        systemError("Service Disconnected",null);
    }

    @Override
    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        StrictMode.ThreadPolicy tp = StrictMode.ThreadPolicy.LAX;
        StrictMode.setThreadPolicy(tp);

        setContentView(R.layout.activity_startup);

        AppContext.getApp().pendingIntent = PendingIntent.getActivity(
                getApplication().getBaseContext(),
                0,
                new Intent(getIntent()),
                0);
    }

    @Override
    protected void onPostCreate(Bundle paramBundle){
        super.onPostCreate(paramBundle);

        connectToService();
    }

    @Override
    public void serviceReady() {
        startActivity(new Intent((Context)this, HomeActivity.class));
        finish();
    }


    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        runOnUiThread(() -> {
            Handler tryAgainHandler = new Handler(Looper.myLooper());

            AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(this);
            alertDialogBuilder.setTitle(paramString);
            alertDialogBuilder.setMessage(paramThrowable.getMessage()).setCancelable(false);
            alertDialogBuilder.setPositiveButton("Try again", (dialog, which) -> {
                tryAgainHandler.removeCallbacksAndMessages(null);
                connectToService();
            });
            AlertDialog alertDialog = alertDialogBuilder.create();
            alertDialog.show();

            Runnable tryAgain = () -> {
                alertDialog.getButton(AlertDialog.BUTTON_POSITIVE).performClick();
            };
            tryAgainHandler.postDelayed(tryAgain, ApplicationConst.RESTART_COUNT_DELAY);
        });
    }
}