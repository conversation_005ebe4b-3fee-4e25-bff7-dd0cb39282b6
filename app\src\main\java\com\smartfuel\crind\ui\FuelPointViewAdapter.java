package com.smartfuel.crind.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.crind.R;
import com.smartfuel.service.models.forecourt.FuelPoint;
import java.util.ArrayList;

public class FuelPointViewAdapter extends RecyclerView.Adapter<FuelPointViewAdapter.ViewHolder> {
    private ItemClickListener mClickListener;

    private ArrayList<FuelPoint> mData;

    private LayoutInflater mInflater;

    FuelPointViewAdapter(Context context, ArrayList<FuelPoint> fuelpointList) {
        this.mInflater = LayoutInflater.from(context);
        this.mData = fuelpointList;
    }

    FuelPoint getItem(int index) {
        return this.mData.get(index);
    }

    public int getItemCount() {
        return this.mData.size();
    }

    public void onBindViewHolder(ViewHolder viewHolder, int index) {
        String str = this.mData.get(index).getState();
        index = Integer.parseInt(this.mData.get(index).getId());
        viewHolder.myPumpNumber.setText(String.valueOf(index));
        if (str.equals("02H")) {
            viewHolder.myTextView.setBackgroundColor(Color.rgb(0, 128, 0));
            viewHolder.myTextView.setText("Ready");
        } else {
            viewHolder.myTextView.setBackgroundColor(Color.rgb(234, 0, 0));
            viewHolder.myTextView.setText("In use");
        }
    }

    public ViewHolder onCreateViewHolder(ViewGroup paramViewGroup, int paramInt) {
        return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_item, paramViewGroup, false));
    }

    void setClickListener(ItemClickListener paramItemClickListener) {
        this.mClickListener = paramItemClickListener;
    }

    public static interface ItemClickListener {
        void onItemClick(View param1View, int param1Int);
    }

    public class ViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
        TextView myTextView;
        TextView myPumpNumber;

        private Animation animation;

        ViewHolder(View param1View) {
            super(param1View);
            this.myTextView = (TextView)param1View.findViewById(R.id.info_text);
            this.myPumpNumber = (TextView)param1View.findViewById(R.id.pumpNumber);
            param1View.setOnClickListener(this);
            animation = AnimationUtils.loadAnimation(param1View.getContext(),R.anim.bounce);
        }

        public void onClick(View view) {
            view.startAnimation(animation);
            if (FuelPointViewAdapter.this.mClickListener != null)
                FuelPointViewAdapter.this.mClickListener.onItemClick(view, getAdapterPosition());
        }
    }
}
