<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="15dp"
    app:cardElevation="10dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_margin="15dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="10dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="#c79845">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Rewards Card"
                    android:textAlignment="center"
                    android:textSize="35dp"
                    android:fontFamily="@font/montserrat_bold"
                    android:textColor="@color/White"
                    android:layout_margin="30dp"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/Black"
                    android:layout_marginBottom="20dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <View
            android:layout_width="120dp"
            android:layout_height="100dp"
            android:background="@color/White"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="-80dp"/>

        <ImageView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/successiful"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="-130dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAlignment="center"
            android:text="Reward Applied!"
            android:textSize="40dp"
            android:layout_margin="10dp"/>

    </LinearLayout>
</androidx.cardview.widget.CardView>