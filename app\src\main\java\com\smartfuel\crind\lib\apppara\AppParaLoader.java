package com.smartfuel.crind.lib.apppara;

import android.os.Environment;

import com.smartfuel.crind.app.BaseApplication;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;

public class AppParaLoader {

    private static final String directory = Environment.getDataDirectory() + "/data/" + BaseApplication.getAppContext().getPackageName() + "/files/";

    public static String getPath(){
        return directory;
    }

    public static String getString(String appParaFile) throws IOException {
        StringBuilder keyBuilder = new StringBuilder();
        try(
                FileInputStream file = new FileInputStream(directory + appParaFile);
                InputStreamReader reader = new InputStreamReader(file);
                BufferedReader br = new BufferedReader(reader);
        ) {
            String key = "";
            while ((key = br.readLine()) != null) {
                keyBuilder.append(key);
            }
        }

        return keyBuilder.toString();
    }

    public static FileInputStream getInputStream(String appParaFile) throws FileNotFoundException {
        return new FileInputStream(directory + appParaFile);
    }
}
