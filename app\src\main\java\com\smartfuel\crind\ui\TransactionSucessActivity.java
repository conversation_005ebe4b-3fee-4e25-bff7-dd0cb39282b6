package com.smartfuel.crind.ui;

import android.content.ComponentName;
import android.os.Bundle;
import android.os.IBinder;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.pax.dal.entity.EBeepMode;
import com.smartfuel.crind.R;
import com.smartfuel.crind.consts.ApplicationConst;
import com.smartfuel.crind.lib.neptune.Sdk;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.util.ArrayList;
import java.util.Arrays;

public class TransactionSucessActivity extends BaseActivity {

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            showAuthorizedGrades();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
        setContentView(R.layout.activity_transaction_sucess);
        TextView transactionInfoView = findViewById(R.id.textTransactionInfo);
        TextView proceedPumpView = findViewById(R.id.textProceedtoPump);

        transactionInfoView.setText(String.format("Transaction #\n%s", new Object[] { getIntent().getStringExtra("trnId") }));
        proceedPumpView.setText(String.format("Proceed to pump %s for fuelling.", new Object[] { getIntent().getStringExtra("fuelPointId") }));

        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);

        Sdk.getInstance().getDal(this).getSys().beep(EBeepMode.FREQUENCE_LEVEL_4, 800);

        connectToService();
    }

    private void showAuthorizedGrades(){
        runOnUiThread(() -> {
            String[] authorizedGrades = getIntent().getStringArrayExtra("authorizedGrades");
            if(authorizedGrades != null) {
                ArrayList<GradePrice> configuredGradePrices = null;
                ArrayList<GradePrice> authorizedGradePrices = new ArrayList<>();
                try {
                    //forecourt refactor
                    configuredGradePrices = getService().getConfiguredGradePrices().getGradePrices();

                    if (configuredGradePrices == null || configuredGradePrices.size() == 0) {
                        //throw new Exception("No Fuel Grades & Prices configured");
                    } else {
                        for (GradePrice gp : configuredGradePrices) {
                            if(Arrays.stream(authorizedGrades).anyMatch(gp.getId()::equals)){
                                authorizedGradePrices.add(gp);
                            }
                        }
                        if(authorizedGradePrices.size() > 0) {
                            View txtAuthorizedGrades = findViewById(R.id.txtAuthorizedGrades);
                            txtAuthorizedGrades.setVisibility(View.VISIBLE);
                            RecyclerView recyclerView = findViewById(R.id.fuelPriceList);
                            recyclerView.setVisibility(View.VISIBLE);
                            recyclerView.setLayoutManager(new GridLayoutManager(getBaseContext(), 2));
                            FuelGradePriceViewAdapter fuelGradeListAdapter = new FuelGradePriceViewAdapter(getBaseContext(), authorizedGradePrices, true);
                            recyclerView.setAdapter(fuelGradeListAdapter);
                        }
                    }
                } catch (Exception e) {
                }
            }
        });
    }
}

