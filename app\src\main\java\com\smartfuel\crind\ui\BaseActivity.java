package com.smartfuel.crind.ui;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.WindowManager;

import androidx.annotation.CallSuper;
import androidx.appcompat.app.AppCompatActivity;

import com.pax.dal.ISys;
import com.smartfuel.crind.BuildConfig;
import com.smartfuel.crind.app.AppContext;
import com.smartfuel.crind.lib.neptune.Sdk;
import com.smartfuel.service.OPTService;

public abstract class BaseActivity extends AppCompatActivity implements ServiceConnection {

    protected static final boolean enableDebug = BuildConfig.DEBUG;

    private static final int UI_ANIMATION_DELAY = 300;
    private static final int HIDE_DELAY = 5000;
    private static final int RESTART_COUNT_DELAY = 1000;
    private static final int COUNT_TOUCH_TOTAL = 10;
    private final Handler mHideHandler = new Handler(Looper.myLooper());
    private final Handler mRestartHandler = new Handler(Looper.myLooper());

    private boolean mVisible;
    private int mCountTouch = 0;

    private View mDecorView;

    private IRestartDelayAction restartAction = null;

    private final Runnable mAutoHideRunnable = () -> hide();
    private final Runnable mRestartCountRunnable = () -> mCountTouch = 0;
    private final Runnable mRestartRunnable = () -> {
        if(restartAction != null)
            restartAction.onRestart();
        Intent intent = new Intent(this, HomeActivity.class);
        this.startActivity(intent);
        this.finish();
    };

    private final Runnable mHidePart2Runnable = () -> {
        // Delayed removal of status and navigation bar
        /*if (Build.VERSION.SDK_INT >= 30) {
            mContentView.getWindowInsetsController().hide(
                    WindowInsets.Type.statusBars() | WindowInsets.Type.navigationBars());*/
        //hideSystemUI();
    };

    private OPTService myOPTService;

    protected OPTService getService(){
        return myOPTService;
    }

    protected void connectToService(){
        Intent intent = new Intent(this, OPTService.class);
        startService(intent);
        bindService(intent, this, BIND_AUTO_CREATE);
    }

    protected void disconnectToService(){
        getService().stopSelf();
        unbindService(this);
    }

    @CallSuper
    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        OPTService.LocalBinder localBinder = (OPTService.LocalBinder)binder;
        myOPTService = localBinder.getServiceInstance();
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
    }

    private void hideSystemUI() {
        final int flags = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LOW_PROFILE;

        this.mDecorView.setSystemUiVisibility(flags); //6022
    }

    private final Runnable mShowPart2Runnable = () -> {
        // Delayed display of UI elements
        //ActionBar actionBar = getSupportActionBar();
        //if (actionBar != null) {
        //    actionBar.show();
        //}
    };

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        if(!BuildConfig.DEBUG){
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);//禁用截屏
        }

        mVisible = true;

        //this.mDecorView = getWindow().getDecorView();
        //hideSystemUI();
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);

        mDecorView = ((ViewGroup)getWindow().getDecorView()).getChildAt(0);
        //this.mDecorView = getWindow().getDecorView();

        // Set up the user interaction to manually show or hide the system UI.
        if(enableDebug)
            mDecorView.setOnClickListener(view -> toggle());

        hide();
    }

    @Override
    protected void onDestroy() {
        //show();
        mHideHandler.removeCallbacks(mRestartCountRunnable);
        mHideHandler.removeCallbacks(mShowPart2Runnable);
        mHideHandler.removeCallbacks(mAutoHideRunnable);
        if(myOPTService != null)
            unbindService(this);
        super.onDestroy();
    }

    private void toggle() {
        if (!mVisible) {
            mCountTouch++;
            if(mCountTouch >= COUNT_TOUCH_TOTAL) {
                show();
                mCountTouch = 0;
            }else{
                mHideHandler.removeCallbacks(mRestartCountRunnable);
                mHideHandler.postDelayed(mRestartCountRunnable, RESTART_COUNT_DELAY);
            }
        }
    }

    public void hide() {
        // Hide UI first
        //ActionBar actionBar = getSupportActionBar();
        //if (actionBar != null) {
        //    actionBar.hide();
        //}
        mVisible = false;

        ISys SysModule = Sdk.getInstance().getDal(AppContext.getAppContext()).getSys();
        SysModule.showNavigationBar(mVisible);
        SysModule.showStatusBar(mVisible);
        SysModule.enableStatusBar(mVisible);

        // Schedule a runnable to remove the status and navigation bar after a delay
        mHideHandler.removeCallbacks(mShowPart2Runnable);
        mHideHandler.postDelayed(mHidePart2Runnable, UI_ANIMATION_DELAY);
    }

    private void show() {
        // Show the system bar
        if (Build.VERSION.SDK_INT >= 30) {
            mDecorView.getWindowInsetsController().show(
                    WindowInsets.Type.statusBars() | WindowInsets.Type.navigationBars());
        } else {
            mDecorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);
        }
        mVisible = true;

        ISys SysModule = Sdk.getInstance().getDal(AppContext.getAppContext()).getSys();
        SysModule.showNavigationBar(mVisible);
        SysModule.showStatusBar(mVisible);
        SysModule.enableStatusBar(mVisible);

        // Schedule a runnable to display UI elements after a delay
        mHideHandler.removeCallbacks(mHidePart2Runnable);
        mHideHandler.postDelayed(mShowPart2Runnable, UI_ANIMATION_DELAY);

        mHideHandler.removeCallbacks(mAutoHideRunnable);
        mHideHandler.postDelayed(mAutoHideRunnable, HIDE_DELAY);
    }

    protected void setRestartCountDelay(long delayMillis) {
        setRestartCountDelay(delayMillis, null);
    }

    protected void setRestartCountDelay(long delayMillis, IRestartDelayAction restartAction){
        this.restartAction = restartAction;
        mRestartHandler.postDelayed(mRestartRunnable, delayMillis);
    }

    protected  void cancelRestart(){
        mRestartHandler.removeCallbacks(mRestartRunnable);
    }

    public interface IRestartDelayAction{
        void onRestart();
    }
}
