package com.smartfuel.crind.lib.neptune;

import android.content.Context;
import android.util.Log;

import com.pax.dal.IDAL;
import com.pax.neptunelite.api.NeptuneLiteUser;

public class Sdk {
    private static final String TAG = "SDK";
    private static Sdk instance = null;
    private IDAL dal;

    private Sdk() {
    }

    public static Sdk getInstance() {
        if (instance == null) {
            instance = new Sdk();
        }
        return instance;
    }

    public IDAL getDal(Context context) {
        if (isPaxDevice()) {
            Log.i(TAG, "before NeptuneUser");
            try {
                dal = NeptuneLiteUser.getInstance().getDal(context);
            } catch (Exception e) {
                Log.w(TAG, e);
            }
            Log.i(TAG, "after NeptuneUser");
        }
        return dal;
    }

    public static boolean isPaxDevice() {
        return true;
    }
}
