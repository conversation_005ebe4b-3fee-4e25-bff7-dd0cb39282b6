plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services'
    id("com.google.firebase.crashlytics")
}

def static releaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("UTC"))
}

android {
    namespace 'com.smartfuel.crind'
    compileSdk 33

    packagingOptions {
        resources {
            exclude 'META-INF/NOTICE.md'
            exclude 'META-INF/LICENSE.md'
        }
    }

    defaultConfig {
        applicationId 'com.smartfuel.crind'
        minSdk 25
        targetSdk 33
        versionCode 11
        versionName "0.00.11" + "_" + releaseTime()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }

        ndk {
            abiFilters 'armeabi'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            buildConfigField('boolean','RELEASE','true')
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
            buildConfigField('boolean','RELEASE','false')
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            if (outputFileName != null && outputFile.name.endsWith('.apk')) {
                def type = ""
                if (variant.buildType.name == 'debug') {
                    type = "_debug"
                }
                def fileName = "SmartFuel_Crind_V${defaultConfig.versionName}${type}.apk"
                outputFileName = fileName
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    testOptions{
        unitTests.returnDefaultValues = true
    }
    buildFeatures {
        viewBinding true
    }
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "androidx.recyclerview:recyclerview:1.2.1"
    implementation 'androidx.appcompat:appcompat:1.6.0'
    implementation 'commons-io:commons-io:2.11.0'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'

    implementation 'commons-validator:commons-validator:1.7'

    //MAX Store
    implementation 'com.whatspos.sdk:paxstore-3rd-app-android-sdk:8.7.2'

    //Firebase
    implementation platform('com.google.firebase:firebase-bom:32.2.2')
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")

    // SHARED PROJECT REFERENCE
    implementation project(':SmartFuelService')

    debugImplementation "com.squareup.leakcanary:leakcanary-android:2.7"
}