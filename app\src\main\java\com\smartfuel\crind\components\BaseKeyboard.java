package com.smartfuel.crind.components;

import android.app.Activity;
import android.content.Context;
import android.inputmethodservice.Keyboard;
import android.inputmethodservice.KeyboardView;
import android.text.Editable;
import android.view.View;
import android.widget.EditText;

import com.smartfuel.crind.R;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class BaseKeyboard extends Keyboard implements KeyboardView.OnKeyboardActionListener, View.OnFocusChangeListener {

    private Context context;

    private View.OnClickListener onSend;
    private View.OnClickListener onCancel;
    private View focusCurrent;

    //private static final int DOTCOM = -1;
    //private static final int DOTAU = -2;
    private static final int DELETE = -3;
    private static final int SEND = -4;
    private static final int CANCEL = -5;

    public BaseKeyboard(Context context, int xmlLayoutResId, View.OnClickListener onSend, View.OnClickListener onCancel){
        super(context, xmlLayoutResId);
        this.context = context;
        this.onSend = onSend;
        this.onCancel = onCancel;
    }

    @Override
    public void onPress(int primaryCode) {
        if(focusCurrent == null)
            focusCurrent = ((Activity)context).getWindow().getCurrentFocus();

        if (focusCurrent == null || !(focusCurrent instanceof EditText)) return;

        EditText edittext = (EditText) focusCurrent;
        Editable editable = edittext.getText();
        int start = edittext.getSelectionStart();

        // Handle key
        if(primaryCode == CANCEL) {
            if (onCancel != null) {
                onCancel.onClick(null);
            }
        }else if(primaryCode == SEND) {
            if(onSend != null){
                onSend.onClick(null);
            }
        } else if(primaryCode == DELETE) {
            if(editable != null && start > 0) editable.delete(start - 1, start);
        /*} else if(primaryCode == DOTCOM) {
            if(editable != null) editable.insert(start, ".com");
        } else if(primaryCode == DOTAU) {
            if(editable != null) editable.insert(start, ".au");*/
        } else if(primaryCode >= 0){// Insert character
            if(editable != null) editable.insert(start, Character.toString((char) primaryCode));
        }
    }

    protected EditText getEditText(){
        return (EditText) focusCurrent;
    }

    @Override
    public void onRelease(int primaryCode) {

    }

    @Override
    public void onKey(int primaryCode, int[] keyCodes) {

    }

    @Override
    public void onText(CharSequence text) {

    }

    @Override
    public void swipeLeft() {

    }

    @Override
    public void swipeRight() {

    }

    @Override
    public void swipeDown() {

    }

    @Override
    public void swipeUp() {

    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if(hasFocus)
            focusCurrent = v;
        else if(focusCurrent == v)
            focusCurrent = null;
    }

    public static class Builder{
        private List<EditText> editTexts;
        private KeyboardView keyboardView;
        private Context context;
        private View.OnClickListener onSend;
        private View.OnClickListener onCancel;

        public Builder addEditText(EditText ...editTexts) {
            if(this.editTexts == null)
                this.editTexts = new ArrayList<>();
            this.editTexts.addAll(Arrays.asList(editTexts));
            return this;
        }

        public Builder setKeyboardView(KeyboardView keyboardView) {
            this.keyboardView = keyboardView;
            this.keyboardView.setPreviewEnabled(false);
            return this;
        }

        public Builder setContext(Context context) {
            this.context = context;
            return this;
        }

        public Builder setOnSend(View.OnClickListener onSend) {
            this.onSend = onSend;
            return this;
        }

        public Builder setOnCancel(View.OnClickListener onCancel) {
            this.onCancel = onCancel;
            return this;
        }

        public <T> void build(Class<T> clazz) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, InstantiationException {
            if(context == null) new Exception("context is mandatory.");
            BaseKeyboard keyboard = (BaseKeyboard)(clazz.getConstructor(Context.class, View.OnClickListener.class, View.OnClickListener.class).newInstance( context, onSend, onCancel));//new BaseKeyboard(context, onSend, onCancel);

            if(keyboardView == null) new Exception("KeyboardView is mandatory.");
            keyboardView.setKeyboard(keyboard);
            keyboardView.setOnKeyboardActionListener(keyboard);

            if(editTexts != null) {
                for (EditText v : editTexts) {
                    v.setShowSoftInputOnFocus(false);
                    v.setOnFocusChangeListener(keyboard);
                }
            }
        }
    }
}
