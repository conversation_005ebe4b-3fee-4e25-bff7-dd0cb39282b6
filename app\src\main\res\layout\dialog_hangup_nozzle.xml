<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="0dp"
    app:cardCornerRadius="15dp"
    app:cardElevation="10dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="15dp"
            app:cardElevation="10dp"
            android:layout_marginBottom="5dp"
            android:layout_marginTop="50dp"
            android:layout_marginHorizontal="50dp"
            app:cardBackgroundColor="@android:color/holo_red_light">
        <TextView
            android:id="@+id/pumpno"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Please hang up nozzle number 01"
            android:textAlignment="center"
            android:textSize="25dp"
            android:layout_margin="20dp"
            android:textColor="@color/White"
            android:textStyle="bold"/>
        </androidx.cardview.widget.CardView>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="20dp">

            <androidx.cardview.widget.CardView
                android:layout_width="200dp"
                android:layout_height="match_parent"
                app:cardCornerRadius="50dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@android:color/holo_red_light"
                android:layout_marginLeft="-70dp"/>

            <androidx.cardview.widget.CardView
                android:layout_width="130dp"
                android:layout_height="100dp"
                app:cardCornerRadius="10dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@color/Black"
                android:layout_marginLeft="-50dp"
                android:layout_marginTop="50dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="FUEL"
                    android:layout_gravity="center_vertical|end"
                    android:textColor="@color/White"
                    android:textSize="40dp"
                    android:layout_marginRight="15dp"/>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="130dp"
                android:layout_height="250dp"
                app:cardCornerRadius="10dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@android:color/holo_red_dark"
                android:layout_marginLeft="-50dp"
                android:layout_marginTop="200dp"/>

            <androidx.cardview.widget.CardView
                android:layout_width="150dp"
                android:layout_height="50dp"
                app:cardCornerRadius="0dp"
                app:cardElevation="10dp"
                app:cardBackgroundColor="@color/Black"
                android:layout_alignParentBottom="true"/>

            <ImageView
                android:id="@+id/pumpimg"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:src="@drawable/nozzle"
                android:adjustViewBounds="true"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"/>

        </RelativeLayout>

    </LinearLayout>
</androidx.cardview.widget.CardView>