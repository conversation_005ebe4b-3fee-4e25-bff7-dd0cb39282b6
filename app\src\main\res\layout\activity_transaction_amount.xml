<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.AppCompat.DayNight.NoActionBar"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    app:behavior_hideable="true"
    android:background="@drawable/bottom_sheet_background"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="15dp">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp">

        <ImageView
            android:id="@+id/pump_image"
            android:layout_width="wrap_content"
            android:layout_height="100dp"
            android:adjustViewBounds="true"
            android:contentDescription="@string/pumpimage"
            app:srcCompat="@drawable/gas_pump"
            android:layout_marginLeft="30dp"/>

        <TextView
            android:id="@+id/pumpNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_bold"
            android:text="@string/pumpNo"
            android:textSize="40dp"
            android:layout_centerHorizontal="true"
            android:layout_alignBottom="@+id/pump_image"
            android:layout_above="@id/pump_image"
            android:textColor="@color/White"
            android:layout_marginBottom="-5dp"/>

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/fuelPriceList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="10dp"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:layout_marginTop="20dp">
        <Button
            android:id="@+id/minusTen"
            style="@style/Button.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="-$10"
            android:textSize="20dp"
            app:cornerRadius="80dp" />
        <EditText
            android:id="@+id/editTextAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:fontFamily="@font/montserrat"
            android:background="@drawable/stroke_box"
            android:hint="@string/amount"
            android:importantForAutofill="no"
            android:inputType="numberDecimal"
            android:textAlignment="center"
            android:textColorHint="#BBBBBB"
            android:textSize="30dp"
            android:padding="5dp"
            android:layout_marginHorizontal="10dp"/>
        <Button
            android:id="@+id/plusTen"
            style="@style/Button.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="+$10"
            android:textSize="20dp"
            app:cornerRadius="80dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginBottom="20dp">
        <Button
            android:id="@+id/clearBtn"
            style="@style/Button.Gray"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/clear"
            android:textSize="12dp"
            app:cornerRadius="80dp" />

        <Button
            android:id="@+id/btnDone"
            style="@style/Button.Blue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:text="@string/done_btn"
            android:textSize="25dp"/>

        <Button
            android:id="@+id/cancelBtn"
            style="@style/Button.Gray"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="CANCEL"
            android:textSize="12dp"
            app:cornerRadius="80dp" />
    </LinearLayout>

</LinearLayout>