package com.smartfuel.crind.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.crind.R;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.util.ArrayList;


public class FuelGradePriceViewAdapter extends RecyclerView.Adapter<FuelGradePriceViewAdapter.ViewHolder> {
    private final ArrayList<GradePrice> mData;

    private final LayoutInflater mInflater;

    private Boolean newLayout;

    FuelGradePriceViewAdapter(Context context, ArrayList<GradePrice> gradePriceList) {
        this(context, gradePriceList, false);
    }

    FuelGradePriceViewAdapter(Context context, ArrayList<GradePrice> gradePriceList, Boolean newLayout) {
        this.mInflater = LayoutInflater.from(context);
        this.mData = gradePriceList;
        this.newLayout = newLayout;
    }

    public int getItemCount() {
        return this.mData.size();
    }

    public void onBindViewHolder(ViewHolder viewHolder, int index) {
        viewHolder.fuelName.setText(((GradePrice)this.mData.get(index)).getName());
        viewHolder.fuelPrice.setText(((GradePrice)this.mData.get(index)).getPrice() + " c/L");
        View viewBack;
        if(!newLayout) {
            viewBack = viewHolder.fuelName;
            viewBack.setBackgroundColor(((GradePrice)this.mData.get(index)).getColor());
        }
        else {
            viewBack = viewHolder.colorIcon;
            ((CardView)viewBack).setCardBackgroundColor(((GradePrice)this.mData.get(index)).getColor());
        }

        /*if(newLayout) {
            if (index % 2 != 0.0)
                viewHolder.dividerH.setVisibility(View.GONE);

            boolean odd = this.mData.size() % 2 == 0.0;
            if ((odd && index >= (this.mData.size() - 2)) || (!odd && index >= (this.mData.size() - 1)))
                viewHolder.dividerV.setVisibility(View.GONE);

            if(mData.size() == 1)
                viewHolder.dividerH.setVisibility(View.GONE);
        }*/
    }

    public ViewHolder onCreateViewHolder(ViewGroup paramViewGroup, int paramInt) {
        if(!newLayout)
            return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_fuel_price, paramViewGroup, false));
        else
            return new ViewHolder(this.mInflater.inflate(R.layout.recyclerview_fuel_price_new, paramViewGroup, false));
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        TextView fuelName;

        TextView fuelPrice;

        View colorIcon;
        //View dividerH;
        //View dividerV;

        ViewHolder(View view) {
            super(view);
            this.fuelName = view.findViewById(R.id.fuelName);
            this.fuelPrice = view.findViewById(R.id.fuelPrice);
            this.colorIcon = view.findViewById(R.id.colorIcon);
            //this.dividerH = view.findViewById(R.id.dividerH);
            //this.dividerV = view.findViewById(R.id.dividerV);
        }
    }
}
