package com.smartfuel.crind.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.smartfuel.crind.ui.StartupActivity;

public class StartupOnBootUpReceiver  extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {

        if(Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            Log.d(StartupOnBootUpReceiver.class.getSimpleName(), "ACTION_BOOT_COMPLETED");

            Intent activityIntent = new Intent(context, StartupActivity.class);
            activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(activityIntent);

        }
    }
}
