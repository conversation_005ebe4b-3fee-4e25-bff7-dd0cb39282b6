<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <requestFocus />

    <RelativeLayout
        android:id="@+id/topbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true">

        <View
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="#1A588E"/>

        <ImageView
            android:id="@+id/btnReceipt"
            android:layout_width="80.0dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:layout_alignParentLeft="true"
            android:src="@drawable/receipt_icon"
            android:layout_margin="10dp"
            android:background="@drawable/circle"
            android:padding="14dp"
            app:tint="#1A588E" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_alignParentEnd="true"
            android:gravity="end"
            android:padding="10dp">

            <TextClock
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:format12Hour="MMMM dd, yyyy"
                android:format24Hour="MMMM dd, yyyy"
                android:text="@string/date"
                android:textColor="@color/White"
                android:textSize="32.0px"
                android:enabled="false"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextClock
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:format12Hour="hh:mm:ss"
                    android:format24Hour="hh:mm:ss"
                    android:text="@string/_00_00_00"
                    android:textColor="@color/White"
                    android:textSize="32.0px"
                    android:enabled="false"/>

                <TextClock
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:format12Hour="a"
                    android:format24Hour="a"
                    android:text="@string/am"
                    android:textColor="@color/White"
                    android:textSize="32.0px"
                    android:enabled="false"/>
            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/card_icons"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_alignParentEnd="true"
        android:layout_alignBottom="@id/topbar">
        <ImageView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:adjustViewBounds="true"
            android:src="@drawable/visa_home_icon"
            android:layout_marginHorizontal="10dp"/>
        <ImageView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:adjustViewBounds="true"
            android:src="@drawable/mastercard_home_icon"
            android:layout_marginHorizontal="10dp"/>
        <ImageView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:adjustViewBounds="true"
            android:src="@drawable/eftpos_home_icon"
            android:layout_marginHorizontal="10dp"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layScreen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_below="@id/topbar"
        android:layout_marginTop="10dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_semibold"
            android:text="@string/pay_fuel_here"
            android:textColor="@color/DarkGrey"
            android:textSize="70.0px"
            android:layout_gravity="center"/>

        <ImageView
            android:id="@+id/imgTouch"
            android:layout_width="150.0dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@string/touch_icon"
            app:srcCompat="@drawable/touchicon"
            android:layout_gravity="center"
            android:layout_marginTop="60dp"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@id/fuelPriceList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"/>

</RelativeLayout>