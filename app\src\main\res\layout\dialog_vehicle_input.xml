<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    app:behavior_hideable="true"
    android:background="@drawable/bottom_sheet_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Enter your vehicle registration"
            android:textAlignment="center"
            android:textSize="20dp"
            android:layout_marginBottom="5dp"/>
        <EditText
            android:id="@+id/txtVehicle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="30dip"
            android:hint="Vehicle Registration..."
            android:background="@drawable/stroke_box"
            android:textAlignment="center"/>
        <TextView
            android:id="@+id/txvMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Invalid Vehicle Registration."
            android:textAlignment="center"
            android:textSize="10dp"
            android:textColor="@android:color/holo_red_light"
            android:layout_marginBottom="5dp"
            android:visibility="invisible"/>
    </LinearLayout>

    <android.inputmethodservice.KeyboardView
        android:id="@+id/keyboardview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"/>
</LinearLayout>