package com.smartfuel.crind.app;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.util.Log;

public class AppInfo {
    private static final String PAXSTORE_APP_KEY = "PAXSTORE_APP_KEY";
    private static final String PAXSTORE_APP_SECRET = "PAXSTORE_APP_SECRET";

    private AppInfo() {
        // do nothing
    }

    public static AppInfo getInstance() {
        return AppInfoHolder.INSTANCE;
    }

    /**
     * 获取paxStore APP_KEY
     */
    public String getAppKey() {
        return getMetaData(PAXSTORE_APP_KEY);
    }

    /**
     * 获取paxStore APP_SECRET
     */
    public String getAppSecret() {
        return getMetaData(PAXSTORE_APP_SECRET);
    }

    /**
     * 获取AndroidManifest文件中metadata
     *
     * @param name key
     * @return string value
     */
    public String getMetaData(String name) {
        ApplicationInfo applicationInfo = null;
        try {
            applicationInfo = BaseApplication.getAppContext()
                    .getPackageManager()
                    .getApplicationInfo(BaseApplication.getAppContext().getPackageName(),
                            PackageManager.GET_META_DATA);
        } catch (PackageManager.NameNotFoundException e) {
            Log.e("AppInfo", "", e);
        }
        if (applicationInfo == null || applicationInfo.metaData == null) {
            return "";
        }
        return applicationInfo.metaData.getString(name);
    }

    /**
     * 获取AndroidManifest文件中metadata
     *
     * @param name key
     * @return int value
     */
    public int getIntMetaData(String name) {
        ApplicationInfo applicationInfo = null;
        try {
            applicationInfo = BaseApplication.getAppContext()
                    .getPackageManager()
                    .getApplicationInfo(BaseApplication.getAppContext().getPackageName(),
                            PackageManager.GET_META_DATA);
        } catch (PackageManager.NameNotFoundException e) {
            Log.e("AppInfo", "", e);
        }
        if (applicationInfo == null || applicationInfo.metaData == null) {
            return 0;
        }
        return applicationInfo.metaData.getInt(name);
    }

    private static class AppInfoHolder {
        private static final AppInfo INSTANCE = new AppInfo();
    }

}
