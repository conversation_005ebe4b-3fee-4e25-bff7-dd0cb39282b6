<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="SplashTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
    </style>


    <style name="Description.Bold" parent="ThemeOverlay.AppCompat">
        <item name="android:textSize">64px</item>
        <item name="android:fontFamily">@font/montserrat_semibold</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="Description.Subtitle" parent="ThemeOverlay.AppCompat">
        <item name="android:textSize">48px</item>
        <item name="android:fontFamily">@font/montserrat_semibold</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textAlignment">center</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>

    </style>

    <style name="Button.Blue" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">#5184D1</item>
        <item name="android:textSize">40px</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MyApp.Button.Rounded</item>
    </style>

    <style name="Button.Gray" parent="ThemeOverlay.AppCompat">
        <item name="android:backgroundTint">#D8D8D8</item>
        <item name="android:layout_marginLeft">20px</item>
        <item name="android:layout_marginRight">20px</item>
        <item name="android:textColor">#727272</item>
        <item name="android:textSize">72px</item>
        <item name="android:paddingTop">20px</item>
        <item name="android:paddingBottom">20px</item>
        <item name="android:fontFamily">@font/montserrat_bold</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MyApp.Button.Rounded</item>
    </style>

    <style name="ShapeAppearanceOverlay.MyApp.Button.Rounded" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">30dp</item>
        <item name="cornerSizeBottomRight">30dp</item>
        <item name="cornerSizeTopLeft">30dp</item>
        <item name="cornerSizeBottomLeft">30dp</item>
    </style>

    <style name="Widget.Theme.Kiosk.ActionBar.Fullscreen" parent="Widget.AppCompat.ActionBar">
        <item name="android:background">@color/black_overlay</item>
    </style>

    <style name="Widget.Theme.Kiosk.ButtonBar.Fullscreen" parent="">
        <item name="android:background">@color/black_overlay</item>
        <item name="android:buttonBarStyle">?android:attr/buttonBarStyle</item>
    </style>


</resources>