package com.smartfuel.crind.ui;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.InsetDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.ViewGroup;
import android.view.Window;

import androidx.annotation.NonNull;

import com.smartfuel.crind.R;

public class RewardAppliedDialog extends Dialog {

    RewardAppliedDialogListener listener;
    final Handler handler  = new Handler();
    final Runnable runnable = new Runnable() {
        @Override
        public void run() {
            if (RewardAppliedDialog.this.isShowing()) {
                listener.onClose();
                RewardAppliedDialog.this.dismiss();
            }
        }
    };

    public RewardAppliedDialog(@NonNull Context context, RewardAppliedDialogListener listener) {
        super(context);
        this.listener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_reward_applied);
        setCancelable(false);

        ColorDrawable back = new ColorDrawable(Color.TRANSPARENT);
        InsetDrawable inset = new InsetDrawable(back, 10);
        Window window = getWindow();
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        window.setBackgroundDrawable(inset);

        setOnDismissListener((o) -> {
            handler.removeCallbacks(runnable);
        });

        handler.postDelayed(runnable, 3000);
    }

    public interface RewardAppliedDialogListener{
        void onClose();
    }
}
