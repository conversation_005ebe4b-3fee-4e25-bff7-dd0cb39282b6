<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Kiosk" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/DarkBlue</item>
        <item name="colorPrimaryVariant">@color/Blue</item>
        <item name="colorOnPrimary">@color/White</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/LightBlue</item>
        <item name="colorSecondaryVariant">@color/Blue</item>
        <item name="colorOnSecondary">@color/Black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="Theme.Kiosk.Fullscreen" parent="Theme.Kiosk">
        <item name="android:actionBarStyle">@style/Widget.Theme.Kiosk.ActionBar.Fullscreen</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
    </style>

    <style name="ThemeOverlay.Kiosk.FullscreenContainer" parent="">
        <item name="fullscreenBackgroundColor">@color/light_blue_600</item>
        <item name="fullscreenTextColor">@color/light_blue_A200</item>
    </style>
</resources>