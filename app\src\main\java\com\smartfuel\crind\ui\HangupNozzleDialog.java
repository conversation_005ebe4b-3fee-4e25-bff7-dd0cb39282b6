package com.smartfuel.crind.ui;

import android.animation.Animator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.InsetDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.animation.DecelerateInterpolator;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.pax.dal.entity.EBeepMode;
import com.smartfuel.crind.R;
import com.smartfuel.crind.lib.neptune.Sdk;

public class HangupNozzleDialog extends Dialog {

    private String pumpnostr;
    private View pumpimg;
    private long startTime;

    public HangupNozzleDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_hangup_nozzle);
        setCancelable(false);

        ColorDrawable back = new ColorDrawable(Color.TRANSPARENT);
        InsetDrawable inset = new InsetDrawable(back, 10);
        getWindow().setBackgroundDrawable(inset);

        TextView pumpno = findViewById(R.id.pumpno);
        pumpimg = findViewById(R.id.pumpimg);

        pumpno.setText("Please hang up nozzle number " + pumpnostr);

        setOnShowListener(dialog -> {
            startTime = System.currentTimeMillis();
            startAnim();
        });
    }

    public void setPumpno(String pumpno){
        pumpnostr = pumpno;
    }

    private void startAnim(){
        float x = pumpimg.getTranslationX();

        pumpimg.animate()
                .translationX( -150)
                .setDuration(2000)
                .setInterpolator(new DecelerateInterpolator())
                .setListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                        long currentTime = System.currentTimeMillis();
                        if(currentTime - startTime > 60000)
                            return;
                        if(HangupNozzleDialog.this.isShowing())
                            Sdk.getInstance().getDal(getContext()).getSys().beep(EBeepMode.FREQUENCE_LEVEL_3, 100);
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        pumpimg.setTranslationX(x);
                        if(HangupNozzleDialog.this.isShowing())
                            startAnim();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {

                    }
                }).start();
    }
}
