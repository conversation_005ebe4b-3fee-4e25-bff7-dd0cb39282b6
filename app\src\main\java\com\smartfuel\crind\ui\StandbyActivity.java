package com.smartfuel.crind.ui;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.crind.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;

public class StandbyActivity extends BaseActivity implements IServiceEvents {

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            StandbyActivity.this.setContentView(R.layout.activity_standby);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
        startActivity(new Intent(StandbyActivity.this,StartupActivity.class));
        finish();
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        Log.e("HomeActivity", "SystemError", paramThrowable);
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    @Override
    public void showHomeScreen() {
        com.smartfuel.service.logger.Log.i("StandbyActivity", "Going Home");
        startActivity(new Intent((Context) this, HomeActivity.class));
        finish();
    }

    @Override
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        connectToService();
    }
}
