package com.smartfuel.crind.app;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.RemoteException;
import android.util.Log;
import android.widget.Toast;

import com.google.firebase.crashlytics.CustomKeysAndValues;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.pax.dal.IDAL;
import com.pax.market.android.app.sdk.BaseApiService;
import com.pax.market.android.app.sdk.StoreSdk;
import com.smartfuel.crind.BuildConfig;
import com.smartfuel.crind.lib.apppara.AppParaProperties;
import com.smartfuel.crind.lib.neptune.Sdk;
import com.smartfuel.crind.utils.ThreadPoolManager;
import com.smartfuel.service.PropertyReader;
import com.smartfuel.service.logger.LoggerService;
import com.smartfuel.service.property.IProperties;

public class AppContext extends BaseApplication {
    private static final String TAG = "App";

    private static AppContext mApp;

    // Neptune interface
    private static IDAL dal;

    // app version
    private static String version;

    public PendingIntent pendingIntent;

    private boolean isReadyToUpdate = true;

    public void onCreate() {
        super.onCreate();

        AppParaProperties.configureParam();

        mApp = this;

        ThreadPoolManager.getInstance().execute(() -> {
            registerActivityLifecycleCallbacks(new AppActivityLifecycleCallbacks());
            init();
            version = updateVersion();
        });

        LoggerService.setAppVersion(BuildConfig.VERSION_NAME);

        initStoreSdk();
        initFirebase();
    }

    private void init(){
        dal = Sdk.getInstance().getDal(this);

        //defaultUEH = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(_unCaughtExceptionHandler);
    }

    private void initStoreSdk() {
        //todo Init AppKey，AppSecret, make sure the appKey and appSecret is corret.
        String key = AppInfo.getInstance().getAppKey();
        String secret = AppInfo.getInstance().getAppSecret();
        StoreSdk.getInstance().init(getApplicationContext(), key, secret,
                new BaseApiService.Callback() {
                    @Override
                    public void initSuccess() {
                        Log.i(TAG, "initSuccess.");
                        initInquirer();
                    }

                    @Override
                    public void initFailed(RemoteException e) {
                        Log.i(TAG, "initFailed: "+e.getMessage());
                        Toast.makeText(getApplicationContext(), "Cannot get API URL from STORE client," +
                                " Please install STORE client first.", Toast.LENGTH_LONG).show();
                    }
                });
    }

    private void initFirebase(){
        /*Setup the User for Analytics reporting*/
        com.smartfuel.service.PropertyReader propertyReader = new PropertyReader(getBaseContext());
        IProperties properties = propertyReader.getMyProperties();
        FirebaseCrashlytics.getInstance().setUserId(properties.getAndroidId(this));

        CustomKeysAndValues keysAndValues = new CustomKeysAndValues.Builder()
                .putString("DeviceId", properties.getAndroidId(this))
                .putString("VersionId", BuildConfig.VERSION_NAME)
                .build();

        FirebaseCrashlytics.getInstance().setCustomKeys(keysAndValues);
    }

    private void initInquirer() {

        //todo 2. Init checking of whether app can be updated
        StoreSdk.getInstance().initInquirer(new StoreSdk.Inquirer() {
            @Override
            public boolean isReadyUpdate() {
                Log.i(TAG, "call business function....isReadyUpdate = " + isReadyToUpdate);
                //todo call your business function here while is ready to update or not
                return isReadyToUpdate;
            }
        });
    }

    /**
     * Gets version.
     *
     * @return the version
     */
    public static String getVersion() {
        return version;
    }

    /**
     * get app version
     */
    private String updateVersion() {
        try {
            PackageManager manager = getPackageManager();
            PackageInfo info = manager.getPackageInfo(getPackageName(), 0);
            return info.versionName;
        } catch (Exception e) {
            Log.w(TAG, e);
            return null;
        }
    }

    //private Thread.UncaughtExceptionHandler defaultUEH;
    // handler listener
    private Thread.UncaughtExceptionHandler _unCaughtExceptionHandler = new Thread.UncaughtExceptionHandler() {
        @Override
        public void uncaughtException(Thread thread, Throwable ex) {
            ex.printStackTrace();

            AlarmManager mgr = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
            mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 500, pendingIntent);
            System.exit(2);
        }
    };

    public static AppContext getApp() {
        return mApp;
    }

    /**
     * Gets dal.
     *
     * @return the dal
     */
    public static IDAL getDal() {
        return dal;
    }
}
