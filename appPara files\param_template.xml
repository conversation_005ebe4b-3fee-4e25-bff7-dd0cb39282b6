<?xml version="1.0" encoding="gb2312"?>
<Schema>
 <Groups>
	<Group> 
      <ID>sys_properties</ID>
      <Title>Properties</Title> 
      <Order>1</Order> 
      <Description/>
    </Group>
 </Groups>
 <!-- Files Definition --> 
 <Files>
    <File> 
      <ID>sys_properties</ID>
      <FileName>kiosk.properties</FileName>
      <Description/> 
    </File>
</Files>
<!-- Parameter Definition --> 
<Parameters>
<!-- Sub Applications --> 
  <Header>
    <Title>Controller Configurations</Title> 
    <DisplayStyle>common</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>pos_id</PID>
      <Title>POS ID</Title> 
      <Length>2</Length>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>POS ID</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter> 
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>IP</DataType> 
      <PID>forecourt_controller_ip</PID>
      <Title>Forecourt Controller IP</Title> 
      <Length>15</Length>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Forecourt Controller IP</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Port</DataType> 
      <PID>forecourt_controller_port</PID>
      <Title>Forecourt Controller Port</Title> 
      <Length>6</Length>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Forecourt Controller Port</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
	  <InputType>select</InputType>
      <PID>forecourt_controller_type</PID>
      <Title>Forecourt Controller Type</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
	  <Select>{"pss5000":"pss5000","POSTEC":"POSTEC","sfcmicro":"sfcmicro"}</Select>
      <Defaultvalue>pss5000</Defaultvalue>
      <Description>Forecourt Controller Type</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter> 
  </Header>
  
  <Header>
    <Title>Kiosk Configurations</Title> 
    <DisplayStyle>common</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>kiosk_id</PID>
      <Title>Kiosk ID</Title> 
      <Length>36</Length>
	  <MinLength>36</MinLength>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Kiosk ID provided by POSMaster</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number_String</DataType> 
      <PID>kiosk_validation_code</PID>
      <Title>Kiosk Validation Code</Title> 
      <Length>6</Length>
	  <MinLength>6</MinLength>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Kiosk Validation Code provided by POSMaster</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>configured_fuel_point</PID>
      <Title>Configured Fuel Point</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Fuel Point's IDs to be made available on the device, separated by a comma, example: 01,02,03</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>store_currency</PID>
      <Title>Store Currency</Title> 
      <Required>true</Required> 
      <Readonly>true</Readonly> 
      <Defaultvalue>36</Defaultvalue>
	  <Length>3</Length>
      <Description>currency code number, provided by ISO 4217, example: AUD=36</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
	  <InputType>select</InputType>
      <PID>payment_service_type</PID>
      <Title>Payment Service Type</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
	  <Select>{"PAXService":"PAXService","USB":"USB"}</Select>
      <Defaultvalue>PAXService</Defaultvalue>
      <Description>Payment Service Type</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
  </Header>
  
  <Header>
    <Title>Fuel Grades Configurations</Title> 
    <DisplayStyle>common</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_01</PID>
      <Title>Fuel Grade 01</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>STechron</Defaultvalue>
      <Description>Fuel Grade 01 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_02</PID>
      <Title>Fuel Grade 02</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>Gasoline</Defaultvalue>
      <Description>Fuel Grade 02 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_03</PID>
      <Title>Fuel Grade 03</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>97Techron</Defaultvalue>
      <Description>Fuel Grade 03 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_04</PID>
      <Title>Fuel Grade 04</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>E10</Defaultvalue>
      <Description>Fuel Grade 04 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_05</PID>
      <Title>Fuel Grade 05</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>Premium 98</Defaultvalue>
      <Description>Fuel Grade 05 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_06</PID>
      <Title>Fuel Grade 06</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>Premium Diesel</Defaultvalue>
      <Description>Fuel Grade 06 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_07</PID>
      <Title>Fuel Grade 07</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>Diesel</Defaultvalue>
      <Description>Fuel Grade 07 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_08</PID>
      <Title>Fuel Grade 08</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>AD Blue</Defaultvalue>
      <Description>Fuel Grade 08 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>FG_09</PID>
      <Title>Fuel Grade 09</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>LPG</Defaultvalue>
      <Description>Fuel Grade 09 name</Description> 
      <Display>true</Display> 
      <GroupID>sys_properties</GroupID> 
      <FileID>sys_properties</FileID>
    </Parameter>
  </Header>
	
</Parameters>
</Schema>