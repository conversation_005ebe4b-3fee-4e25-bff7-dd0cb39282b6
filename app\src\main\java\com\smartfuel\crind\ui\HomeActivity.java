package com.smartfuel.crind.ui;

import android.animation.Animator;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.smartfuel.crind.R;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.GradePrice;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;

public class HomeActivity extends BaseActivity implements IServiceEvents {

    private Animation animation;

    private View btnReceipt;
    private View layScreen;

    private HangupNozzleDialog hangupNozzleDialog;

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialise();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void initialise(){
        HomeActivity.this.setContentView(R.layout.activity_home);
        layScreen = findViewById(R.id.layScreen);
        layScreen.setOnClickListener(v -> {onClickScreen(v);});
        btnReceipt = findViewById(R.id.btnReceipt);
        btnReceipt.setOnClickListener(v -> {onReceiptClick(v);});
        animation = AnimationUtils.loadAnimation(this,R.anim.bounce);
        gradePriceUpdate();
        startAnimTouch();
    }

    @Override
    public void onServiceDisconnected(ComponentName componentName) {
        startActivity(new Intent(HomeActivity.this,StartupActivity.class));
        finish();
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        Log.e("HomeActivity", "SystemError", paramThrowable);
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    @Override
    public void gradePriceUpdate(){
        runOnUiThread(() -> {
            ArrayList<GradePrice> configuredGradePrices = null;
            try {
                //forecourt refactor
                configuredGradePrices = getService().getConfiguredGradePrices().getGradePrices();

                if (configuredGradePrices == null || configuredGradePrices.size() == 0) {
                    //throw new Exception("No Fuel Grades & Prices configured");
                } else {
                    RecyclerView recyclerView = HomeActivity.this.findViewById(R.id.fuelPriceList);
                    recyclerView.setLayoutManager(new GridLayoutManager(HomeActivity.this.getBaseContext(), 2));
                    FuelGradePriceViewAdapter fuelGradeListAdapter = new FuelGradePriceViewAdapter(HomeActivity.this.getBaseContext(), configuredGradePrices, true);
                    recyclerView.setAdapter(fuelGradeListAdapter);
                }
            } catch (Exception e) {
                getService().Error(e);
            }
        });
    }

    @Override
    public void fuellingStatusUpdate(){
        ArrayList<FuelPoint> fps = getService().getConfiguredFuelPoints();
        boolean isShow = false;
        for (FuelPoint fp : fps) {
            if(fp.isNozzleLifted()){
                isShow = true;
                if(!hangupNozzleDialog.isShowing()) {
                    runOnUiThread(() -> {
                        hangupNozzleDialog.setPumpno(Integer.parseInt(fp.getId()) + "");
                        hangupNozzleDialog.show();
                    });
                }
                break;
            }
        }
        if(!isShow && hangupNozzleDialog.isShowing())
            hangupNozzleDialog.dismiss();
    }

    @Override
    public void showStandbyScreen(){
        Intent standbyIntent = new Intent((Context)this, StandbyActivity.class);
        startActivity(standbyIntent);
        finish();
    }

    public void onClickScreen(View paramView) {
        layScreen.startAnimation(animation);
        startActivity(new Intent(this, PumpSelectActivity.class));
        finish();
    }

    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        connectToService();
        hangupNozzleDialog = new HangupNozzleDialog(this);
    }

    public void onReceiptClick(View view) {
        btnReceipt.startAnimation(animation);
        startActivity(new Intent(this, PresentReceiptActivity.class));
        finish();
    }

    private void startAnimTouch(){
        findViewById(R.id.imgTouch).animate()
                .scaleY(1.3f)
                .scaleX(1.3f)
                .setDuration(2000)
                .setInterpolator(new DecelerateInterpolator())
                .setListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        finishAnimTouch();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {

                    }
                }).start();
    }

    private void finishAnimTouch(){
        findViewById(R.id.imgTouch).animate()
                .scaleY(1f)
                .scaleX(1f)
                .setDuration(2000)
                .setInterpolator(new DecelerateInterpolator())
                .setListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        startAnimTouch();
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {

                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {

                    }
                }).start();
    }
}
