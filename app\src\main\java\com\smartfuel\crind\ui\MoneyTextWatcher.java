package com.smartfuel.crind.ui;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;

import com.smartfuel.service.utils.CurrencyConverter;

import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.text.NumberFormat;

public class MoneyTextWatcher implements TextWatcher {
    WeakReference<EditText> editTextWeakReference;

    public MoneyTextWatcher(EditText paramEditText) {
        this.editTextWeakReference = new WeakReference<>(paramEditText);
    }

    public void afterTextChanged(Editable paramEditable) {
        EditText editText = this.editTextWeakReference.get();
        if (editText == null)
            return;
        String str2 = paramEditable.toString();
        if (str2.isEmpty())
            return;
        editText.removeTextChangedListener(this);
        BigDecimal bigDecimal = (new BigDecimal(str2.replaceAll("[$,.]", ""))).setScale(2, 3).divide(new BigDecimal(100), 3);
        String str1 = CurrencyConverter.getFormatter().format(bigDecimal);
        editText.setText(str1);
        editText.setSelection(str1.length());
        editText.addTextChangedListener(this);
    }

    public void beforeTextChanged(CharSequence paramCharSequence, int paramInt1, int paramInt2, int paramInt3) {}

    public void onTextChanged(CharSequence paramCharSequence, int paramInt1, int paramInt2, int paramInt3) {}
}

