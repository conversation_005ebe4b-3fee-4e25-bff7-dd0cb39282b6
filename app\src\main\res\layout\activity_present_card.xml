<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#1A588E"
        android:padding="10dp"
        android:id="@+id/topbar">

        <TextView
            style="@style/Description.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Payment Method"
            android:layout_marginBottom="30dp"
            android:textColor="@color/White"
            android:textSize="20dp"
            android:layout_gravity="center_horizontal"/>
    </FrameLayout>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.3"
        app:cardCornerRadius="10dp"
        app:cardElevation="10dp"
        android:background="@color/White"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="30dp"
        android:layout_above="@id/topbar"
        android:layout_marginTop="-30dp"
        android:padding="10dp">
        <TextView
            android:id="@+id/txtAmount"
            style="@style/Description.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$10.00"
            android:layout_gravity="center"
            android:textColor="#1A588E"/>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/btnBankCard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardCornerRadius="10dp"
        app:cardElevation="10dp"
        android:background="@color/White"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="30dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_gravity="center">

            <TextView
                style="@style/Description.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Credit/Debit Card"
                android:textSize="20dp"/>

            <!--ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/bank_card"
                android:layout_centerInParent="true"
                android:layout_margin="30dp"/-->

            <LinearLayout
                android:id="@+id/card_icons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginHorizontal="30dp">

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:adjustViewBounds="true"
                    android:src="@drawable/visa_home_icon"
                    android:layout_marginHorizontal="10dp"/>
                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:adjustViewBounds="true"
                    android:src="@drawable/mastercard_home_icon"
                    android:layout_marginHorizontal="10dp"/>
                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:adjustViewBounds="true"
                    android:src="@drawable/eftpos_home_icon"
                    android:layout_marginHorizontal="10dp"/>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/btnLocalCard"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardCornerRadius="10dp"
        app:cardElevation="10dp"
        android:background="@color/White"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="30dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_gravity="center">

            <TextView
                android:id="@+id/txtLocalCard"
                style="@style/Description.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Local Card"
                android:textSize="20dp"
                android:layout_marginTop="20dp"/>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/fleet_card"
                android:layout_above="@id/txtLocalCard"
                android:layout_marginTop="-120dp"
                android:layout_marginBottom="-100dp"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>