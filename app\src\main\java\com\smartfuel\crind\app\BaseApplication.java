package com.smartfuel.crind.app;

import android.app.Application;
import android.content.Context;
import android.os.Handler;

import com.smartfuel.crind.utils.SharedPrefUtil;
import com.smartfuel.crind.utils.ThreadPoolManager;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

public class BaseApplication extends Application {
    private static BaseApplication mBaseApplication ;
    private static Handler handler;
    private ExecutorService backgroundExecutor;

    public static SharedPrefUtil appPreferences;

    @Override
    public void onCreate() {
        super.onCreate();
        appPreferences = new SharedPrefUtil(getApplicationContext()); // this Preference comes for free from the library
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        mBaseApplication = this;
        //initWMRouter();
        ThreadPoolManager.getInstance().getExecutor().prestartAllCoreThreads();
        backgroundExecutor = ThreadPoolManager.getInstance().getExecutor();
        backgroundExecutor.execute(() -> {
            //EventProxy.getInstance().init(new RxBusImpl());
            //MultiDex.install(mBaseApplication);
        });
        handler = new Handler();
    }

    public static BaseApplication getAppContext(){
        return mBaseApplication;
    }

    public void runOnUiThread(final Runnable runnable) {
        handler.post(runnable);
    }

    public void runOnUiThreadDelay(final Runnable runnable, long delayMillis) {
        handler.postDelayed(runnable, delayMillis);
    }

    public void runInBackground(final Runnable runnable) {
        backgroundExecutor.execute(runnable);
    }

    public <V> Future<V> runInBackground(final Callable<V> callable) {
        return backgroundExecutor.submit(callable);
    }
}
