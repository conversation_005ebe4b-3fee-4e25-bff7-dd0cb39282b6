<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    app:behavior_hideable="true"
    android:background="@drawable/bottom_sheet_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="10dp"
            app:cardElevation="10dp"
            app:cardBackgroundColor="#c79845">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Rewards Card"
                    android:textAlignment="center"
                    android:textSize="35dp"
                    android:fontFamily="@font/montserrat_bold"
                    android:textColor="@color/White"
                    android:layout_margin="30dp"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/Black"
                    android:layout_marginBottom="20dp"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
        <TextView
            android:id="@+id/txtMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Would you like to use a Reward Card?"
            android:textAlignment="center"
            android:textSize="20dp"
            android:layout_margin="20dp"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_marginBottom="20dp">

            <Button
                android:id="@+id/btnYes"
                style="@style/Button.Blue"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="YES"
                android:textSize="25dp"/>

            <Button
                android:id="@+id/btnNo"
                style="@style/Button.Gray"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="NO"
                android:textSize="25dp"
                app:cornerRadius="80dp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>