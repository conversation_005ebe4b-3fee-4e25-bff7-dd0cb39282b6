<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@color/White"
    android:layout_margin="5dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/colorIcon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:background="@color/Blue"
            android:layout_margin="5dp"
            app:cardCornerRadius="15dp"
            app:cardElevation="0dp"
            android:layout_gravity="center"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingHorizontal="5dp"
            android:paddingTop="5dp">

            <TextView
                android:id="@+id/fuelName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/fuel_name"
                android:textSize="28px"
                android:textColor="#1A588E"
                android:fontFamily="@font/montserrat_semibold"
                android:textStyle="" />

            <TextView
                android:id="@+id/fuelPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/fuel_price_sample"
                android:textSize="32px"
                android:textColor="#1A588E"/>

        </LinearLayout>

        <!--View
            android:id="@+id/dividerV"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#02a1e3"
            android:layout_marginTop="5dp"
            android:layout_marginHorizontal="10dp"/-->
    </LinearLayout>

    <!--View
        android:id="@+id/dividerH"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="#02a1e3"
        android:layout_marginVertical="10dp"/-->

</androidx.cardview.widget.CardView>