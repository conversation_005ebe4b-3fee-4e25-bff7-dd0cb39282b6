package com.smartfuel.crind.lib.apppara;

import android.content.Context;
import android.provider.Settings;

import androidx.annotation.NonNull;

import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.pax.dal.IDAL;
import com.pax.dal.entity.ETermInfoKey;
import com.pax.market.android.app.sdk.StoreSdk;
import com.pax.market.api.sdk.java.base.exception.NotInitException;
import com.pax.market.api.sdk.java.base.exception.ParseXMLException;
import com.smartfuel.crind.BuildConfig;
import com.smartfuel.crind.lib.neptune.Sdk;
import com.smartfuel.service.PropertyReader;
import com.smartfuel.service.property.IProperties;

import org.apache.commons.io.FileUtils;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;

public class AppParaProperties extends LinkedHashMap<String, String> implements IProperties {

    @Override
    public void load(InputStream inStream) throws IOException {
        try {
            super.putAll(getParametersXml(inStream));
        }catch (Exception e){
            throw  new IOException(e);
        }
    }

    @Override
    public void load(File file) throws IOException {
        try {
            super.putAll(getParameters(file));
        }catch (Exception e){
            throw new IOException(e);
        }
    }

    @Override
    public String getProperty(String key) {
        return getProperty(key, "");
    }

    @Override
    public String getProperty(String key, String defaultValue) {
        if(super.containsKey(key))
            return super.get(key);
        else
            return defaultValue;
    }

    @Override
    public String getAndroidId(Context context) {
        IDAL dal = Sdk.getInstance().getDal(context);
        return dal.getSys().getTermInfo().get(ETermInfoKey.SN);
    }

    private LinkedHashMap<String, String> getParametersXml(InputStream stream) throws XmlPullParserException, IOException {
        XmlPullParserFactory factory = XmlPullParserFactory.newInstance();
        factory.setNamespaceAware(true);
        XmlPullParser xpp = factory.newPullParser();

        xpp.setInput(stream, null);
        int eventType = xpp.getEventType();
        String key = "";
        String value = "";
        LinkedHashMap<String, String> ret = new LinkedHashMap<>();
        while (eventType != XmlPullParser.END_DOCUMENT) {
            if (eventType == XmlPullParser.START_DOCUMENT) {
                //System.out.println("Start document");
            } else if (eventType == XmlPullParser.START_TAG) {
                //System.out.println("Start tag " + xpp.getName());
                key = xpp.getName();
            } else if (eventType == XmlPullParser.TEXT) {
                //System.out.println("Text " + xpp.getText());
                value = xpp.getText();
            } else if (eventType == XmlPullParser.END_TAG) {
                //System.out.println("End tag " + xpp.getName());
                if(!key.equals("") && !xpp.getName().equals("parameter")) {
                    ret.put(key, value);
                    key = "";
                    value = "";
                }
            }

            eventType = xpp.next();
        }
        return ret;
        //System.out.println("End document");
    }

    @NonNull
    private LinkedHashMap<String, String> getParameters(File parameterFile) {
        try {
            //parse the download parameter xml file for display.
            //todo call API to parse xml
            if (isJsonFile(parameterFile)) {
                return StoreSdk.getInstance().paramApi().parseDownloadParamJsonWithOrder(parameterFile);
            } else {
                return StoreSdk.getInstance().paramApi().parseDownloadParamXmlWithOrder(parameterFile);
            }
        } catch (JsonParseException e) {
            e.printStackTrace();
        } catch (NotInitException e) {
            e.printStackTrace();
            try{
                return getParametersXml(new FileInputStream(parameterFile));
            } catch (FileNotFoundException ex) {
                e.printStackTrace();
            } catch (XmlPullParserException ex) {
                e.printStackTrace();
            } catch (IOException ex) {
                e.printStackTrace();
            }
        } catch (ParseXMLException e) {
            e.printStackTrace();
        }

        return null;
    }

    private boolean isJsonFile(File parameterFile) {
        if (parameterFile == null) {
            return false;
        }
        try {
            String jsonStr = FileUtils.readFileToString(parameterFile, StandardCharsets.UTF_8);
            JsonElement jsonElement = (new JsonParser()).parse(jsonStr);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        } catch (IOException e1) {
            return false;
        }
    }

    public static void configureParam(){
        PropertyReader.setPropertiesHandle(new PropertyReader.IPropertiesHandle() {
            @Override
            public IProperties getHandle() {
                return new AppParaProperties();
            }

            @Override
            public IProperties loadProperties(Context context, IProperties properties, String paramString) throws IOException {
                if(!paramString.equals("")) {
                    String paraFiles = AppParaLoader.getPath();
                    File f = new File(paraFiles + paramString);
                    if (f.exists()) {
                        if (BuildConfig.DEBUG)
                            properties.load(new FileInputStream(f));
                        else
                            properties.load(f);
                        return properties;
                    } else {
                        throw new RuntimeException("Parameter files do not exist.");
                    }
                } else {
                    return properties;
                }
            }
        });
    }
}
