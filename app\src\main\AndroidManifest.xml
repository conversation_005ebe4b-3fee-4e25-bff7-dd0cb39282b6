<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".app.AppContext"
        android:allowBackup="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Kiosk"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true">

        <meta-data android:name="PAXSTORE_APP_KEY" android:value="AFQZ5JZQP0ZLNMLG2CLE" />
        <meta-data android:name="PAXSTORE_APP_SECRET" android:value="FXSSOG4A9M3RE05GQ29NSBQHO2HSR8IBUURF21UM" />

        <!--receiver
            android:name=".receiver.StartupOnBootUpReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver-->

        <service
            android:name="com.smartfuel.service.OPTService"
            android:exported="true">
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>
        </service>

        <service android:name=".service.DownloadParamService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.sdk.service.ACTION_TO_DOWNLOAD_PARAMS"/>
                <category android:name="${applicationId}"/>
            </intent-filter>
        </service>

        <activity
            android:name=".ui.TransactionDeclinedActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:label="@string/title_activity_transaction_declined"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.TransactionSucessActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.SystemErrorActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.PumpSelectActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.PresentReceiptActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.PresentPaymentActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.HomeActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.StandbyActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" />
        <activity
            android:name=".ui.StartupActivity"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>

</manifest>