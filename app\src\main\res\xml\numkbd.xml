<?xml version="1.0" encoding="utf-8"?>
<Keyboard xmlns:android="http://schemas.android.com/apk/res/android"
    android:keyWidth="20%p"
    android:horizontalGap="0px"
    android:verticalGap="0px"
    android:keyHeight="50dp">

    <Row android:rowEdgeFlags="top">
        <Key android:codes="49" android:keyLabel="1" android:keyEdgeFlags="left" android:horizontalGap="20%p"  />
        <Key android:codes="50" android:keyLabel="2" />
        <Key android:codes="51" android:keyLabel="3" android:keyEdgeFlags="right"/>
    </Row>

    <Row>
        <Key android:codes="52" android:keyLabel="4" android:keyEdgeFlags="left" android:horizontalGap="20%p"/>
        <Key android:codes="53" android:keyLabel="5" />
        <Key android:codes="54" android:keyLabel="6" android:keyEdgeFlags="right"/>
    </Row>

    <Row>
        <Key android:codes="55" android:keyLabel="7" android:keyEdgeFlags="left" android:horizontalGap="20%p"/>
        <Key android:codes="56" android:keyLabel="8"/>
        <Key android:codes="57" android:keyLabel="9" android:keyEdgeFlags="right"/>
    </Row>

    <Row>
        <Key android:codes="48" android:keyLabel="0" android:keyEdgeFlags="left" android:keyWidth="40%p" android:horizontalGap="20%p"/>
        <Key android:codes="-3" android:keyLabel="DEL" android:keyEdgeFlags="right"/>
    </Row>

    <Row android:rowEdgeFlags="bottom">
        <Key android:codes="-4" android:keyLabel="Send" android:keyEdgeFlags="left" android:keyWidth="50%p"/>
        <Key android:codes="-5" android:keyLabel="Skip" android:keyEdgeFlags="right" android:keyWidth="50%p"/>
    </Row>
</Keyboard>