<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="20dp">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/error"
        android:layout_marginBottom="50dp"
        android:layout_marginHorizontal="50dp"/>

    <TextView
        android:id="@id/textProceedtoPump"
        style="@style/Description.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pump_instruction" />

    <TextView
        style="@style/Description.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Please re-try your fuel purchase." />

</LinearLayout>