package com.smartfuel.crind.ui;

import android.content.ComponentName;
import android.content.Intent;
import android.inputmethodservice.KeyboardView;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.smartfuel.crind.R;
import com.smartfuel.crind.components.BaseKeyboard;
import com.smartfuel.crind.components.EmailKeyboard;
import com.smartfuel.crind.consts.ApplicationConst;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.models.transaction.CardReceipt;

import org.apache.commons.validator.routines.EmailValidator;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

public class PresentReceiptActivity extends BaseActivity implements IServiceEvents {

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialize();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        connectToService();

        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);
    }

    private void initialize(){
        //setContentView(R.layout.activity_present_card);
        setContentView(R.layout.activity_present_card_temp);

        View btnBankCard = this.findViewById(R.id.btnBankCard);
        btnBankCard.setOnClickListener(v -> {
            cancelRestart();
            try {
                getService().initialiseReceipt();
            } catch (Exception e) {
                systemError("No Receipt Data", e);
            }
        });

        Handler handler = new Handler();
        handler.postDelayed(() -> {
            btnBankCard.performClick();
        }, 2000);
    }

    @Override
    public void cardReceiptComplete(List<Integer> terminalTransactionId){
        setRestartCountDelay(60000);
        getService().PrepareCustomerReceipt(terminalTransactionId, "CARD");
    }

    @Override
    public void customerCardReceiptDataReady(List<CardReceipt> cardReceipts){
        openDialogEmail(cardReceipts);
    }

    private void openDialogEmail(List<CardReceipt> cardReceipts){
        runOnUiThread(() -> {
            BottomSheetDialog dialog = new BottomSheetDialog(this, R.style.BottomSheetDialogTheme);
            dialog.setContentView(R.layout.dialog_email_input);
            dialog.setCancelable(false);
            dialog.getBehavior().setPeekHeight(1880);

            EditText txtEmail = dialog.findViewById(R.id.txtEmail);
            KeyboardView mKeyboardView= dialog.findViewById(R.id.keyboardview);
            TextView txvMessage = dialog.findViewById(R.id.txvMessage);

            try {
                new BaseKeyboard.Builder()
                        .addEditText(txtEmail)
                        .setContext(this)
                        .setKeyboardView(mKeyboardView)
                        .setOnSend((v) -> {
                            txvMessage.setVisibility(View.INVISIBLE);
                            String text = txtEmail.getText().toString().trim();
                            if(!text.isEmpty() && EmailValidator.getInstance()
                                    .isValid(text)) {
                                dialog.dismiss();
                                try {
                                    getService().sendTransactionReceipt(cardReceipts, text);
                                    serviceReady();
                                } catch (Exception e) {
                                    systemError("sendTransactionReceipt", e);
                                }
                            } else{
                                txvMessage.setVisibility(View.VISIBLE);
                            }
                        })
                        .setOnCancel((v) -> {
                            dialog.dismiss();
                            serviceReady();
                        })
                        .build(EmailKeyboard.class);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            dialog.show();
        });
    }

    public void serviceReady() {
        Intent intent = new Intent(this, HomeActivity.class);
        startActivity(intent);
        finish();
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent(this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }
}
