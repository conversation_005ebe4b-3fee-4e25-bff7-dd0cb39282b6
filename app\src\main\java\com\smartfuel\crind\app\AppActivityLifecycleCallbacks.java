package com.smartfuel.crind.app;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.util.Log;

public class AppActivityLifecycleCallbacks implements Application.ActivityLifecycleCallbacks {

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        ActivityStack.getInstance().push(activity);
        Log.i("Lifecycle::onCreated", "currentActivity:"+activity);
    }

    @Override
    public void onActivityStarted(Activity activity) {
        // do nothing
    }

    @Override
    public void onActivityResumed(Activity activity) {
        ActivityStack.getInstance().setTop(activity);
        Log.i("Lifecycle::onResumed", "currentActivity:"+activity);
    }

    @Override
    public void onActivityPaused(Activity activity) {
        // do nothing
    }

    @Override
    public void onActivityStopped(Activity activity) {
        // do nothing
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
        // do nothing
    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        ActivityStack.getInstance().popWithoutFinish(activity);
        Log.i("Lifecycle::onDestroyed", "currentActivity:"+activity);
    }
}
