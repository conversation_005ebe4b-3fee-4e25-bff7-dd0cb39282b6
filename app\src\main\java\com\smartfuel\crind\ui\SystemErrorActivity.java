package com.smartfuel.crind.ui;

import android.os.Bundle;
import android.widget.TextView;

import com.smartfuel.crind.BuildConfig;
import com.smartfuel.crind.R;
import com.smartfuel.crind.consts.ApplicationConst;

public class SystemErrorActivity extends BaseActivity {

    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.activity_system_error);
        TextView textView = findViewById(R.id.errorTextView);
        if (BuildConfig.DEBUG) {
            textView.setText(getIntent().getStringExtra("ErrorMessage") + "\n" + getIntent().getStringExtra("ErrorStackTrace"));
        } else {
            textView.setText(getIntent().getStringExtra("ErrorMessage"));
        }

        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);
    }
}
