<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="25dp">

    <TextView
        android:id="@id/textTransactionInfo"
        style="@style/Description.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_corner"
        android:paddingVertical="45dp"
        android:paddingHorizontal="25dp"
        android:text="@string/ticket_no"
        android:textColor="#ffffffff"
        app:cornerRadius="80.0px" />

    <TextView
        android:id="@id/textProceedtoPump"
        style="@style/Description.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/pump_instruction" />

    <TextView
        style="@style/Description.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/thank_you_messages" />

    <TextView
        android:id="@+id/txtAuthorizedGrades"
        style="@style/Description.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Authorized Grades:"
        android:visibility="gone"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@id/fuelPriceList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"/>
</LinearLayout>