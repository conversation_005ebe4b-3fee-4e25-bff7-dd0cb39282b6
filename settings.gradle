pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        jcenter()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        jcenter()
    }
}
rootProject.name = "Crind"
include ':app', ":SmartFuelService"
project(':SmartFuelService').projectDir = new File(settingsDir, '../opt-service/Service')
