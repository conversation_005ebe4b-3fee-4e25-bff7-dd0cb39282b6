package com.smartfuel.crind.ui;

import android.os.Bundle;
import android.widget.TextView;

import com.smartfuel.crind.R;
import com.smartfuel.crind.consts.ApplicationConst;


/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 */
public class TransactionDeclinedActivity extends BaseActivity {
    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
        setContentView(R.layout.activity_transaction_declined);
        TextView proceedPumpView = findViewById(R.id.textProceedtoPump);
        // start process to auto print declined receipt

        proceedPumpView.setText(String.format("Transaction Failed.\n Card\n%s \nBank Response\n%s", getIntent().getStringExtra("CardSignature"), getIntent().getStringExtra("BankResponse")));

        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);
    }
}