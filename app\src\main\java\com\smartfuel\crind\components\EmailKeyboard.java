package com.smartfuel.crind.components;

import android.content.Context;
import android.text.Editable;
import android.view.View;
import android.widget.EditText;

import com.smartfuel.crind.R;

public class EmailKeyboard extends BaseKeyboard {

    private static final int DOTCOM = -1;
    private static final int DOTAU = -2;

    public EmailKeyboard(Context context, View.OnClickListener onSend, View.OnClickListener onCancel){
        super(context, R.xml.hexkbd, onSend, onCancel);
    }

    @Override
    public void onPress(int primaryCode) {
        super.onPress(primaryCode);

        EditText edittext = getEditText();
        Editable editable = edittext.getText();
        int start = edittext.getSelectionStart();

        if(primaryCode == DOTCOM) {
            if(editable != null) editable.insert(start, ".com");
        } else if(primaryCode == DOTAU) {
            if(editable != null) editable.insert(start, ".au");
        }
    }
}
