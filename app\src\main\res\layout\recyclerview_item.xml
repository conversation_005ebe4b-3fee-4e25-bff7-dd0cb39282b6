<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="10dp"
    android:background="@color/White">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingTop="20dp">

            <ImageView
                android:id="@+id/pump_image"
                android:layout_width="wrap_content"
                android:layout_height="70dp"
                android:adjustViewBounds="true"
                android:src="@drawable/gas_pump"
                android:layout_marginLeft="12dp"/>

            <TextView
                android:id="@+id/pumpNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_bold"
                android:text="@string/pumpNo"
                android:textSize="30dp"
                android:layout_above="@id/pump_image"
                android:textColor="@color/White"
                android:layout_marginTop="-42dp"/>

            <TextView
                android:id="@+id/info_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="24dp"
                android:textStyle="bold"
                android:paddingVertical="5dp"
                android:textColor="@color/White"
                android:layout_marginTop="10dp"/>
        </LinearLayout>

</androidx.cardview.widget.CardView>