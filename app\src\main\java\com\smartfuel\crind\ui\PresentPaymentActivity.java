package com.smartfuel.crind.ui;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.inputmethodservice.KeyboardView;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.smartfuel.crind.R;
import com.smartfuel.crind.components.BaseKeyboard;
import com.smartfuel.crind.components.CharKeyboard;
import com.smartfuel.crind.components.NumKeyboard;
import com.smartfuel.crind.consts.ApplicationConst;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.models.kiosk.response.AccountCardCapabilities;
import com.smartfuel.service.sqlite.models.Transaction;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Locale;

public class PresentPaymentActivity extends BaseActivity implements IServiceEvents {
    protected long processAmount;
    protected String pumpNo;
    protected AccountCardCapabilities capabilities;
    protected long externalReference;
    protected String im30Reference;

    @Override
    public void onServiceConnected(ComponentName componentName, IBinder binder) {
        super.onServiceConnected(componentName, binder);
        try{
            getService().registerClient(this);
            initialize();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private long trnAmountConversion(String stringAmount) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.US);
        long longAmount = 0L;
        if (numberFormat instanceof DecimalFormat)
            ((DecimalFormat) numberFormat).setParseBigDecimal(true);
        try {
            longAmount = numberFormat.parse(stringAmount.replaceAll("[^\\d]", "")).longValue();
        } catch (ParseException parseException) {
            startActivity(new Intent(this, PumpSelectActivity.class));
            finish();
        }
        return longAmount;
    }

    @Override
    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);
    }

    @Override
    protected void onPostCreate(Bundle paramBundle){
        super.onPostCreate(paramBundle);
        connectToService();
        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);
    }

    private void initialize(){
        //if(getService().isWhitecardEnabled())
        //    setContentView(R.layout.activity_present_card);
        //else
        setContentView(R.layout.activity_present_card_temp);

        View btnBankCard = this.findViewById(R.id.btnBankCard);
        btnBankCard.setOnClickListener(v -> {
            cancelRestart();
            try {
                getService().initialiseTransaction(pumpNo, processAmount); // service will raise an event when the IM30 Terminal responds. Event will navigate user to transaction in progress screen.
            } catch (InterruptedException e) {
                systemError("Service Unavailable",e);
            }
        });

        /*View btnLocalCard = this.findViewById(R.id.btnLocalCard);
        if(btnLocalCard != null) {
            btnLocalCard.setOnClickListener(v -> {
                cancelRestart();
                openDialogVehicle();
                //getService().initialiseWhiteCardTransaction(pumpNo, processAmount, vehicleRegistration, vehicleOdometer); // service will raise an event when the IM30 Terminal responds. Event will navigate user to transaction in progress screen.
            });
        }*/

        this.pumpNo = getIntent().getStringExtra("selectedPump");
        this.processAmount = trnAmountConversion(getIntent().getStringExtra("trnAmount"));

        //if(getService().isWhitecardEnabled()) {
        //    TextView txtAmount = this.findViewById(R.id.txtAmount);
        //    txtAmount.setText(String.format("$ %.2f", processAmount/100.0));
        //}
        //else {
            Handler handler = new Handler();
            handler.postDelayed(() -> {
                btnBankCard.performClick();
            }, 2000);
        //}
    }

    public void serviceReady() {
        Intent intent = new Intent(this, HomeActivity.class);
        startActivity(intent);
        finish();
    }

    public void systemError(String paramString, Throwable paramThrowable) {
        Log.e("TransactionActivity", "SystemError", paramThrowable);
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    @Override
    public void pumpAuthorised(long paramLong, int paramInt, String[] authorizedGrades) {
        Log.i("pumpAuthorised", "TransactionId: " + String.valueOf(paramLong));
        Log.i("pumpAuthorised", "FuelPointId:" + String.valueOf(paramInt));
        Intent intent = new Intent((Context) this, TransactionSucessActivity.class);
        intent.putExtra("trnId", String.valueOf(paramLong));
        intent.putExtra("fuelPointId", String.valueOf(paramInt));
        intent.putExtra("authorizedGrades", authorizedGrades);
        startActivity(intent);
        finish();
    }

    @Override
    public void cardTransactionDeclined(String bankResponse, String cardMask) {
        Intent intent = new Intent((Context) this, TransactionDeclinedActivity.class);
        intent.putExtra("CardSignature", cardMask);
        intent.putExtra("BankResponse", bankResponse);
        startActivity(intent);
        finish();
    }

    @Override
    public void paymentComplete(Transaction transaction) {
        getService().publishKioskTransaction(transaction);
    }

    @Override
    public void getWhiteCardUserInformation(long externalReference, String im30Reference, AccountCardCapabilities capabilities){
        this.externalReference = externalReference;
        this.im30Reference = im30Reference;
        this.capabilities = capabilities;
        cancelRestart();
        openDialogVehicle();
    }

    @Override
    public void getRewardCardUserInformation(long externalReference){
        this.externalReference = externalReference;
        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);
        openDialogReward("");
    }

    @Override
    public void rewardCardApplied(){
        runOnUiThread(() -> {
            RewardAppliedDialog d = new RewardAppliedDialog(this, () ->{
                try {
                    getService().ProceedPrepaidTransaction(externalReference);
                } catch (InterruptedException e) {
                    systemError("Error to proceed prepaid transaction.", e);
                }
            });
            d.show();
        });
    }

    @Override
    public void rewardCardDeclined(String message){
        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY);
        openDialogReward(message + "\nWould you like to try again?");
    }

    @Override
    public void cardMagneticComplete(String track2){
        getService().validateRewardCard(externalReference, track2);
    }

    @Override
    public void cardMagneticDeclined(String message){
        rewardCardDeclined(message);
    }

    private void openDialogReward(String message){
        runOnUiThread(() -> {
            BottomSheetDialog dialog = new BottomSheetDialog(this, R.style.BottomSheetDialogTheme);
            dialog.setContentView(R.layout.dialog_rewardcard);
            dialog.setCancelable(false);
            dialog.getBehavior().setPeekHeight(1880);

            TextView txtMessage = dialog.findViewById(R.id.txtMessage);
            Button btnYes = dialog.findViewById(R.id.btnYes);
            Button btnNo = dialog.findViewById(R.id.btnNo);

            if(!message.isEmpty())
                txtMessage.setText(message);

            btnYes.setOnClickListener((b) -> {
                cancelRestart();
                try {
                    getService().initialiseMagnetic();
                } catch (InterruptedException e) {
                    systemError("Error to read reward card.", e);
                }
                dialog.dismiss();
            });

            btnNo.setOnClickListener((b) -> {
                cancelRestart();
                try {
                    getService().ProceedPrepaidTransaction(externalReference);
                } catch (InterruptedException e) {
                    systemError("Error to proceed prepaid transaction.", e);
                }
                dialog.dismiss();
            });

            dialog.show();
        });
    }

    private void openDialogVehicle(){
        if(!capabilities.isPromptVehicle()) {
            openDialogOdometer();
            return;
        }

        runOnUiThread(() -> {
            BottomSheetDialog dialog = new BottomSheetDialog(this, R.style.BottomSheetDialogTheme);
            dialog.setContentView(R.layout.dialog_vehicle_input);
            dialog.setCancelable(false);
            dialog.getBehavior().setPeekHeight(1880);

            EditText txtVehicle = dialog.findViewById(R.id.txtVehicle);
            KeyboardView mKeyboardView= dialog.findViewById(R.id.keyboardview);
            TextView txvMessage = dialog.findViewById(R.id.txvMessage);

            try {
                new BaseKeyboard.Builder()
                        .addEditText(txtVehicle)
                        .setContext(this)
                        .setKeyboardView(mKeyboardView)
                        .setOnSend((v) -> {
                            txvMessage.setVisibility(View.INVISIBLE);
                            String text = txtVehicle.getText().toString().trim();
                            if(!text.isEmpty()) {
                                capabilities.setInputVehicleRegistration(text);
                                if (capabilities.validateVehicle()) {
                                    dialog.dismiss();
                                    openDialogOdometer();
                                    return;
                                }
                            }
                            txvMessage.setVisibility(View.VISIBLE);
                        })
                        .setOnCancel((v) -> {
                            dialog.dismiss();
                            serviceReady();
                        })
                        .build(CharKeyboard.class);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            dialog.show();
        });
    }

    private void openDialogOdometer(){
        if(!capabilities.isPromptOdometer()) {
            validateTransaction();
            return;
        }

        runOnUiThread(() -> {
            BottomSheetDialog dialog = new BottomSheetDialog(this, R.style.BottomSheetDialogTheme);
            dialog.setContentView(R.layout.dialog_odometer_input);
            dialog.setCancelable(false);
            dialog.getBehavior().setPeekHeight(1880);

            EditText txtOdometer = dialog.findViewById(R.id.txtOdometer);
            KeyboardView mKeyboardView= dialog.findViewById(R.id.keyboardview);
            TextView txvMessage = dialog.findViewById(R.id.txvMessage);

            try {
                new BaseKeyboard.Builder()
                        .addEditText(txtOdometer)
                        .setContext(this)
                        .setKeyboardView(mKeyboardView)
                        .setOnSend((v) -> {
                            txvMessage.setVisibility(View.INVISIBLE);
                            capabilities.setInputOdometer(txtOdometer.getText().toString().trim());
                            if(capabilities.validateOdometer()) {
                                validateTransaction();
                                dialog.dismiss();
                                return;
                            }
                            txvMessage.setVisibility(View.VISIBLE);
                        })
                        .setOnCancel((v) -> {
                            txvMessage.setVisibility(View.INVISIBLE);
                            capabilities.setInputOdometer(null);
                            if(capabilities.validateOdometer()) {
                                validateTransaction();
                                dialog.dismiss();
                                return;
                            }
                            txvMessage.setVisibility(View.VISIBLE);
                        })
                        .build(NumKeyboard.class);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            dialog.show();
        });
    }

    private void validateTransaction(){
        try {
            getService().validateTransaction(externalReference, im30Reference, capabilities); // service will raise an event when the IM30 Terminal responds. Event will navigate user to transaction in progress screen.
        } catch (Exception e) {
            systemError("Service Unavailable",e);
        }
    }
}
