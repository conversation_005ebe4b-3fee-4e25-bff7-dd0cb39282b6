package com.smartfuel.crind.ui;

import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.smartfuel.crind.R;
import com.smartfuel.crind.consts.ApplicationConst;
import com.smartfuel.service.IServiceEvents;
import com.smartfuel.service.models.forecourt.FuelPoint;
import com.smartfuel.service.models.forecourt.GradePrice;
import com.smartfuel.service.utils.CurrencyConverter;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class PumpSelectActivity extends BaseActivity implements FuelPointViewAdapter.ItemClickListener, IServiceEvents {

    FuelPointViewAdapter fuelPointViewAdapter;

    ArrayList<FuelPoint> fuelPoints;

    ArrayList<GradePrice> gradePrices;

    EditText trnAmountText;

    BottomSheetDialog dialog;
    HangupNozzleDialog hangupNozzleDialog;

    private final String inicialAmount = "10.00";

    @Override
    public void onServiceConnected(ComponentName param1ComponentName, IBinder param1IBinder) {
        super.onServiceConnected(param1ComponentName, param1IBinder);
        try{
            getService().registerClient(this);
            gradePriceUpdate();
            configureFuelPoints();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void systemError(String paramString, Throwable paramThrowable) {
        closePumpDialog();
        if(hangupNozzleDialog.isShowing())
            hangupNozzleDialog.dismiss();
        Log.e("PumpSelectActivity", "SystemError", paramThrowable);
        StringWriter stringWriter = new StringWriter();
        paramThrowable.printStackTrace(new PrintWriter(stringWriter));
        Intent intent = new Intent((Context) this, SystemErrorActivity.class);
        intent.putExtra("ErrorMessage", paramString);
        intent.putExtra("ErrorStackTrace", stringWriter.toString());
        startActivity(intent);
        finish();
    }

    @Override
    public void fuellingStatusUpdate(){
        runOnUiThread(() -> {
            closePumpDialog();
            configureFuelPoints();
        });
    }

    @Override
    public void gradePriceUpdate(){
        runOnUiThread(() -> {
            gradePrices = getService().getConfiguredGradePrices().getGradePrices();
            closePumpDialog();
        });
    }

    private void closePumpDialog(){
        if (PumpSelectActivity.this.dialog != null) {
            runOnUiThread(() -> {
                PumpSelectActivity.this.dialog.dismiss();
                PumpSelectActivity.this.dialog = null;
            });
        }
    }

    private void configureFuelPoints(){
        try {
            //forecourt refactor
            fuelPoints = getService().getConfiguredFuelPoints();

            RecyclerView recyclerView = PumpSelectActivity.this.findViewById(R.id.FuelPoints);
            recyclerView.setLayoutManager(
                    new GridLayoutManager(
                            PumpSelectActivity.this,
                            fuelPoints.size() == 1 ? 1 : 2)
            );

            PumpSelectActivity.this.fuelPointViewAdapter =
                    new FuelPointViewAdapter(
                            PumpSelectActivity.this,
                            PumpSelectActivity.this.fuelPoints);
            PumpSelectActivity.this.fuelPointViewAdapter.setClickListener(
                    PumpSelectActivity.this);

            recyclerView.setAdapter(PumpSelectActivity.this.fuelPointViewAdapter);

            boolean isShow = false;
            for (FuelPoint fp : fuelPoints) {
                if(fp.isNozzleLifted()){
                    isShow = true;
                    if(!hangupNozzleDialog.isShowing()) {
                        runOnUiThread(() -> {
                            hangupNozzleDialog.setPumpno(Integer.parseInt(fp.getId()) + "");
                            hangupNozzleDialog.show();
                        });
                        cancelRestart();
                    }
                    break;
                }
            }
            if(!isShow && hangupNozzleDialog.isShowing())
                hangupNozzleDialog.dismiss();

            if (!isShow && fuelPoints.stream().count() == 1) {
                recyclerView.postDelayed(() -> {
                    if(recyclerView.findViewHolderForAdapterPosition(0) != null && recyclerView.findViewHolderForAdapterPosition(0).itemView != null)
                        new Handler().postDelayed(() -> recyclerView.findViewHolderForAdapterPosition(0).itemView.performClick(), 1);
                }, 50);
            }


        } catch (Exception e) {
            getService().Error(e);
        }
    }

    private void createDialog(String pumpNo, List<FuelPoint> fpData, List<GradePrice> fpGradeData) {
        dialog = new BottomSheetDialog(PumpSelectActivity.this, R.style.BottomSheetDialogTheme);
        dialog.setContentView(R.layout.activity_transaction_amount);
        dialog.getBehavior().setPeekHeight(1880);
        //dialog.setCancelable(false);
        startRestart();
        dialog.show();
        trnAmountText = dialog.findViewById(R.id.editTextAmount);
        trnAmountText.addTextChangedListener(new MoneyTextWatcher(trnAmountText));
        trnAmountText.setKeyListener(null);
        trnAmountText.setText(inicialAmount);
        Button plusTen = dialog.findViewById(R.id.plusTen);
        Button minusTen = dialog.findViewById(R.id.minusTen);
        Button cancelBtn = dialog.findViewById(R.id.cancelBtn);
        Button btnDone = dialog.findViewById(R.id.btnDone);
        Button clearBtn = dialog.findViewById(R.id.clearBtn);
        TextView pumpNumber = dialog.findViewById(R.id.pumpNumber);

        RecyclerView fuelRecyclerView = dialog.findViewById(R.id.fuelPriceList);

        FuelPoint f = fpData.stream().filter(fuelPoint -> fuelPoint.getId().equals(String.format("%1$" + 2 + "s", pumpNo).replace(' ', '0'))).findFirst().orElse(null);
        ArrayList<GradePrice> fuelGrades = f.getGradePrices();
        ArrayList<GradePrice> myPumpGrades = new ArrayList<>();
        // this is the list of available fuel grades at this pump - iterate this list and create a vector of grades to show
        for (GradePrice fg : fuelGrades){
            GradePrice fuelPointFuelGrade = fpGradeData.stream().filter(fuelGrade-> fg.getId().equals(fuelGrade.getId())).findFirst().orElse(null);
            myPumpGrades.add(fuelPointFuelGrade);
        }
        FuelGradePriceViewAdapter fuelGradePriceViewAdapter = new FuelGradePriceViewAdapter(this, myPumpGrades, true);

        fuelRecyclerView.setLayoutManager(new GridLayoutManager(this, myPumpGrades.size() == 1 ? 1 : 2));
        fuelRecyclerView.setAdapter(fuelGradePriceViewAdapter);

        pumpNumber.setText(Integer.parseInt(pumpNo) + "");

        assert plusTen != null;
        plusTen.setOnClickListener(v -> calcAmount(10));
        assert minusTen != null;
        minusTen.setOnClickListener(v -> calcAmount(-10));
        assert cancelBtn != null;
        cancelBtn.setOnClickListener(v -> dialog.dismiss());

        btnDone.setOnClickListener(v -> {

            if (!trnAmountText.getText().toString().equals("")) {
                cancelRestart();
                String amount = getValueAmount();
                if (Float.valueOf(amount) < 10.00) {
                    showAlertDialog();
                } else {
                    Intent i = new Intent(PumpSelectActivity.this, PresentPaymentActivity.class);
                    // add the selected pump and Amount to the intent so it is available in present payment activity
                    //pumpNumber
                    //trnAmountText
                    i.putExtra("selectedPump", pumpNumber.getText());
                    i.putExtra("trnAmount", trnAmountText.getText().toString());
                    startActivity(i);
                    dialog.dismiss();
                    finish();
                }
            } else {
                showAlertDialog();
            }

        });

        clearBtn.setOnClickListener(v -> trnAmountText.setText(inicialAmount));

    }

    protected String getValueAmount(){
        String symbol = CurrencyConverter.getFormatter().getCurrency().getSymbol();
        return (trnAmountText.getText().toString()).replace(symbol, "").replace("$", "").replace(",", "");
    }

    protected void calcAmount(int value){
        String currentStr = getValueAmount();
        float currentFlt = Float.parseFloat(currentStr);
        currentFlt += value;

        if(currentFlt < 10)
            currentFlt = 10;

        trnAmountText.setText(String.format(java.util.Locale.US,"%.2f", currentFlt));
    }

    protected void onCreate(Bundle paramBundle) {
        super.onCreate(paramBundle);

        connectToService();
        setContentView(R.layout.activity_pump_select);
        hangupNozzleDialog = new HangupNozzleDialog(this);

        startRestart();
    }

    private void startRestart(){
        setRestartCountDelay(ApplicationConst.RESTART_COUNT_DELAY, () -> {
            closePumpDialog();
            hangupNozzleDialog.dismiss();
        });
    }

    public void onItemClick(View paramView, int index) {
        FuelPoint fp = this.fuelPointViewAdapter.getItem(index);
        String str = fp.getState();
        if (str.equals("02H") && (dialog == null || (dialog != null && !dialog.isShowing()))) {
            cancelRestart();
            createDialog(fp.getId(), this.fuelPoints, this.gradePrices);
        }
    }

    public void showAlertDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder((Context)this);
        builder.setTitle("Invalid amount").setMessage("Minimum amount is $10.00").setCancelable(false).setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface param1DialogInterface, int param1Int) {}
        });
        builder.create().show();
    }
}
